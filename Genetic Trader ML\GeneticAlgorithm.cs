using System;
using System.Collections.Generic;
using System.Linq;

namespace GeneticTraderML.Algorithms
{
    public class GeneticAlgorithm
    {
        private readonly Random _random;
        private readonly double _crossoverRate;
        private readonly double _mutationRate;
        private readonly int _tournamentSize;

        public int Generation { get; private set; }
        public double AverageFitness { get; private set; }
        public double BestFitness { get; private set; }

        public GeneticAlgorithm(double crossoverRate = 0.8, double mutationRate = 0.1, int tournamentSize = 3)
        {
            _random = new Random(Guid.NewGuid().GetHashCode());
            _crossoverRate = crossoverRate;
            _mutationRate = mutationRate;
            _tournamentSize = tournamentSize;
            Generation = 0;
        }

        public void Evolve(List<Trader> population)
        {
            if (population.Count < 2) return;

            var newGeneration = new List<Trader>();
            
            // Elitism - keep best performers
            var eliteCount = Math.Max(1, population.Count / 10);
            newGeneration.AddRange(SelectElites(population, eliteCount));

            // Create rest of population through selection and crossover
            while (newGeneration.Count < population.Count)
            {
                if (_random.NextDouble() < _crossoverRate)
                {
                    var parent1 = TournamentSelection(population);
                    var parent2 = TournamentSelection(population);
                    
                    var child = Crossover(parent1, parent2);
                    newGeneration.Add(child);
                }
                else
                {
                    // Clone a good performer
                    newGeneration.Add(TournamentSelection(population).Clone());
                }
            }

            // Apply mutation
            foreach (var trader in newGeneration.Skip(eliteCount))
            {
                if (_random.NextDouble() < _mutationRate)
                {
                    Mutate(trader);
                }
            }

            // Replace old population
            for (int i = 0; i < population.Count; i++)
            {
                population[i] = newGeneration[i];
                population[i].Reset();
            }

            UpdateStatistics(population);
            Generation++;
        }

        private List<Trader> SelectElites(List<Trader> population, int count)
        {
            return population.OrderByDescending(t => t.Fitness)
                           .Take(count)
                           .Select(t => t.Clone())
                           .ToList();
        }

        private Trader TournamentSelection(List<Trader> population)
        {
            var tournament = population.OrderBy(x => _random.NextDouble())
                                      .Take(_tournamentSize)
                                      .ToList();

            return tournament.OrderByDescending(t => t.Fitness).First().Clone();
        }

        private Trader Crossover(Trader parent1, Trader parent2)
        {
            var child = new Trader();
            var parent1Weights = parent1.Brain.GetWeights();
            var parent2Weights = parent2.Brain.GetWeights();
            
            var childWeights = new double[parent1Weights.Length];

            // Uniform crossover
            for (int i = 0; i < parent1Weights.Length; i++)
            {
                childWeights[i] = _random.NextDouble() < 0.5 ? parent1Weights[i] : parent2Weights[i];
            }

            child.Brain.SetWeights(childWeights);
            return child;
        }

        private void Mutate(Trader trader)
        {
            var weights = trader.Brain.GetWeights();
            var mutationPoint = _random.Next(weights.Length);
            
            // Gaussian mutation
            var gaussian = Math.Sqrt(-2.0 * Math.Log(_random.NextDouble())) * 
                          Math.Cos(2.0 * Math.PI * _random.NextDouble());
            
            weights[mutationPoint] += gaussian * 0.1; // Small mutation
            
            // Ensure weights stay in reasonable range
            weights[mutationPoint] = Math.Max(-1, Math.Min(1, weights[mutationPoint]));
            
            trader.Brain.SetWeights(weights);
        }

        private void UpdateStatistics(List<Trader> population)
        {
            AverageFitness = population.Average(t => t.Fitness);
            BestFitness = population.Max(t => t.Fitness);
        }
    }
}